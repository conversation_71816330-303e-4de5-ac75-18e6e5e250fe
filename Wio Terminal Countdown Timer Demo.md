# Wio Terminal Countdown Timer Demo

Step 1 - Quick Understanding of LEIA Framework: Please briefly explain the basic JSON structure of the LEIA declarative UI framework (refer to [https://github.com/tianrking/LEIA](https://github.com/tianrking/LEIA)), including:

+ states (state definitions)
+ elements (UI elements)
+ actions (action mappings)
+ Based on 1_welcome_screen.json

Step 2 - Reference Button Documentation: Following the standard usage from [https://wiki.seeedstudio.com/Wio-Terminal-Buttons/](https://wiki.seeedstudio.com/Wio-Terminal-Buttons/):

+ Use WIO_KEY_A/B/C definitions, note that the order of the 3 buttons on top of Wio Terminal from left to right is C-B-A, so the prompt content order should also be consistent.
+ INPUT_PULLUP mode, LOW level detection
+ Simple 200ms debounce handling

Step 3 - Direct Code Output: Create a Wio Terminal countdown timer with requirements:

+ Interface: Declarative UI framework
+ Display prompts and three virtual buttons in English, consider visibility and aesthetics of prompts, avoid text being too large to exceed screen, keep prompts concise
+ Function: Physical buttons C/B/A correspond to 30/10/5 second countdown, button prompt information should be positioned at the top of the screen near the buttons, consistent with button order
+ Display: Show large numbers during countdown, but consider aesthetics and visibility, automatically return to initial interface after completion
+ Interaction: Boot into main interface, pressing any of the top 3 buttons will start corresponding countdown, pressing any key during countdown will interrupt current timing and return to main interface

Provide directly:

1. Complete Arduino .ino code
2. Simple usage instructions

Limitations:

+ Keep functionality simple and practical, avoid over-design
+ Ensure code can be directly copy-pasted for use
+ All prompt messages use English

## Reference Code:
### 1_welcome_screen.json
```json
{
  "screen_id": "welcome_screen_worker",
  "background_color": "#ECEFF1",
  "layout": {
    "type": "column",
    "padding": 20,
    "gap": 15,
    "item_alignment": [
      "center_horizontal"
    ],
    "distribution": "center"
  },
  "elements": [
    {
      "type": "label",
      "id": "title_worker",
      "text": "UI Preview (CF Worker)",
      "style_ref": "theme.font.large_title",
      "text_color": "#37474F",
      "horizontal_alignment": "center"
    },
    {
      "type": "container",
      "id": "info_card_worker",
      "style_ref": "theme.card",
      "padding": "15px",
      "width": "90%",
      "layout": {
        "type": "column",
        "gap": 8,
        "item_alignment": [
          "center_horizontal"
        ]
      },
      "elements": [
        {
          "type": "label",
          "text": "Enter JSON config on the left,",
          "style_ref": "theme.font.normal_text"
        },
        {
          "type": "label",
          "text": "then click the 'Render/Refresh Preview' button.",
          "style_ref": "theme.font.normal_text"
        }
      ]
    },
    {
      "type": "button",
      "id": "example_button_worker",
      "label": "Example Button",
      "style_ref": "theme.button.primary",
      "action_id": "SAMPLE_ACTION_WORKER"
    }
  ]
}
```

### RLE_Font_test.ino
```cpp
/*
    Display all the fonts.
This sketch uses the GLCD (font 1) and fonts 2, 4, 6, 7, 8

#########################################################################
###### DON'T FORGET TO UPDATE THE User_Setup.h FILE IN THE LIBRARY ######
######       TO SELECT THE FONTS AND PINS YOU USE, SEE ABOVE       ######
#########################################################################
*/
// New background colour
#define TFT_BROWN 0x38E0
// Pause in milliseconds between screens, change to 0 to time font rendering
#define WAIT 1000
#include <TFT_eSPI.h> // Graphics and font library for ILI9341 driver chip
#include <SPI.h>
TFT_eSPI tft = TFT_eSPI();  // Invoke library, pins defined in User_Setup.h
unsigned long targetTime = 0; // Used for testing draw times
void setup(void) {
    tft.init();
    tft.setRotation(1);
}
void loop() {
    targetTime = millis();
// First we test them with a background colour set
tft.setTextSize(1);
tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 16, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 32, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 48, 2);
int xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 64, 2);
tft.drawChar(127, xpos, 64, 2);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 26, 4);
tft.drawString("<=>?@ABCDE", 0, 52, 4);
tft.drawString("FGHIJKLMNO", 0, 78, 4);
tft.drawString("PQRSTUVWX", 0, 104, 4);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 26, 4);
tft.drawString("pqrstuvwxyz", 0, 52, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 78, 4);
tft.drawChar(127, xpos, 78, 4);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_BLUE, TFT_BLACK);

tft.drawString("012345", 0, 0, 6);
tft.drawString("6789", 0, 40, 6);
tft.drawString("apm-:.", 0, 80, 6);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_RED, TFT_BLACK);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_YELLOW, TFT_BLACK);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 72, 8);
delay(WAIT);;

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 72, 8);
delay(WAIT);;

tft.setTextSize(2);
tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 32, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 64, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 96, 2);
xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 128, 2);
tft.drawChar(127, xpos, 128, 2);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 52, 4);
tft.drawString("<=>?@ABCDE", 0, 104, 4);
tft.drawString("FGHIJKLMNO", 0, 156, 4);
tft.drawString("PQRSTUVWX", 0, 208, 4);
delay(WAIT);
tft.fillScreen(TFT_BLACK);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 52, 4);
tft.drawString("pqrstuvwxyz", 0, 104, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 156, 4);
tft.drawChar(127, xpos, 156, 4);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_BLUE, TFT_BLACK);

tft.drawString("01234", 0, 0, 6);
tft.drawString("56789", 0, 80, 6);
tft.drawString("apm-:.", 0, 160, 6);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_RED, TFT_BLACK);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_YELLOW, TFT_BLACK);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 144, 8);
delay(WAIT);;

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 144, 8);
delay(WAIT);;

tft.setTextColor(TFT_MAGENTA, TFT_BROWN);

tft.drawNumber(millis() - targetTime, 0, 180, 4);
delay(4000);;

// Now test them with transparent background
targetTime = millis();

tft.setTextSize(1);
tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 16, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 32, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 48, 2);
xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 64, 2);
tft.drawChar(127, xpos, 64, 2);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 26, 4);
tft.drawString("<=>?@ABCDE", 0, 52, 4);
tft.drawString("FGHIJKLMNO", 0, 78, 4);
tft.drawString("PQRSTUVWX", 0, 104, 4);

delay(WAIT);
tft.fillScreen(TFT_BROWN);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 26, 4);
tft.drawString("pqrstuvwxyz", 0, 52, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 78, 4);
tft.drawChar(127, xpos, 78, 4);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_BLUE);

tft.drawString("012345", 0, 0, 6);
tft.drawString("6789", 0, 40, 6);
tft.drawString("apm-:.", 0, 80, 6);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_RED);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_YELLOW);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 72, 8);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 72, 8);
delay(WAIT);

tft.setTextSize(2);
tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 32, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 64, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 96, 2);
xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 128, 2);
tft.drawChar(127, xpos, 128, 2);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 52, 4);
tft.drawString("<=>?@ABCDE", 0, 104, 4);
tft.drawString("FGHIJKLMNO", 0, 156, 4);
tft.drawString("PQRSTUVWX", 0, 208, 4);
delay(WAIT);
tft.fillScreen(TFT_BROWN);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 52, 4);
tft.drawString("pqrstuvwxyz", 0, 104, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 156, 4);
tft.drawChar(127, xpos, 156, 4);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_BLUE);

tft.drawString("01234", 0, 0, 6);
tft.drawString("56789", 0, 80, 6);
tft.drawString("apm-:.", 0, 160, 6);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_RED);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_YELLOW);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 144, 8);
delay(WAIT);;

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 144, 8);
delay(WAIT);;

tft.setTextColor(TFT_MAGENTA);

tft.drawNumber(millis() - targetTime, 0, 180, 4);
delay(4000);;
}
```



### Configurable Buttons Example Code
```cpp
void setup() {
    Serial.begin(115200);
    pinMode(WIO_KEY_A, INPUT_PULLUP);
    pinMode(WIO_KEY_B, INPUT_PULLUP);
    pinMode(WIO_KEY_C, INPUT_PULLUP);
}
void loop() {
    // put your main code here, to run repeatedly:
    if (digitalRead(WIO_KEY_A) == LOW) {
        Serial.println("A Key pressed");
    }
    else if (digitalRead(WIO_KEY_B) == LOW) {
        Serial.println("B Key pressed");
    }
    else if (digitalRead(WIO_KEY_C) == LOW) {
        Serial.println("C Key pressed");
    }
    delay(200);
}
```

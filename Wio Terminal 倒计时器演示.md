# Wio Terminal 倒计时器提示词
第一步 - 快速了解LEIA框架: 请简要说明LEIA声明式UI框架的基本JSON结构（参考 [https://github.com/tianrking/LEIA），包括：](https://github.com/tianrking/LEIA），包括：)

+ states（状态定义）
+ elements（UI元素）
+ actions（动作映射）

第二步 - 参考按钮文档: 按照 [https://wiki.seeedstudio.com/Wio-Terminal-Buttons/](https://wiki.seeedstudio.com/Wio-Terminal-Buttons/) 的标准用法：

+ 使用 WIO_KEY_A/B/C 定义，注意 Wio Terminal 顶部3 个按钮的从左到右的顺序是C-B-A，所以提示内容的顺序也要保持一致。
+ INPUT_PULLUP 模式，LOW 电平检测
+ 简单的200ms防抖处理

第三步 - 直接输出代码: 创建Wio Terminal倒计时器，要求：

+ 界面：手动解析 JSON（使用 ArduinoJson 库）来定义 UI 布局，遵循 LEIA 的声明式风格（states、elements、actions），提示按钮我希望做成按钮风格，参考：1_welcome_screen.json
+ 使用英文显示提示信息和三个虚拟按钮，要考虑提示信息的可见性和美观，避免文字过大超出屏幕，提示信息尽量简洁
+ 功能：物理按键C/B/A对应30/10/5秒倒计时，按键提示信息应该位置在屏幕上方靠近按钮的地方，并和按键顺序一致
+ 显示：倒计时时显示大数字，但要考虑美观和可见性，结束后自动返回初始界面
+ 交互：开机进入主界面，按顶部任意 3 个按钮会启动对应的倒计时，倒计时中按任意键会中断当前计时返回主界面

直接提供：

1. 完整的Arduino .ino 代码
2. 简单的使用说明

限制：

+ 功能简洁实用，避免过度设计
+ 确保代码可以直接复制粘贴使用
+ 所有提示信息使用英文

## 参考代码：
### 1_welcome_screen.json
```json
{
  "screen_id": "welcome_screen_worker",
  "background_color": "#ECEFF1",
  "layout": {
    "type": "column",
    "padding": 20,
    "gap": 15,
    "item_alignment": [
      "center_horizontal"
    ],
    "distribution": "center"
  },
  "elements": [
    {
      "type": "label",
      "id": "title_worker",
      "text": "UI Preview (CF Worker)",
      "style_ref": "theme.font.large_title",
      "text_color": "#37474F",
      "horizontal_alignment": "center"
    },
    {
      "type": "container",
      "id": "info_card_worker",
      "style_ref": "theme.card",
      "padding": "15px",
      "width": "90%",
      "layout": {
        "type": "column",
        "gap": 8,
        "item_alignment": [
          "center_horizontal"
        ]
      },
      "elements": [
        {
          "type": "label",
          "text": "Enter JSON config on the left,",
          "style_ref": "theme.font.normal_text"
        },
        {
          "type": "label",
          "text": "then click the 'Render/Refresh Preview' button.",
          "style_ref": "theme.font.normal_text"
        }
      ]
    },
    {
      "type": "button",
      "id": "example_button_worker",
      "label": "Example Button",
      "style_ref": "theme.button.primary",
      "action_id": "SAMPLE_ACTION_WORKER"
    }
  ]
}
```

### RLE_Font_test.ino
```cpp
/*
    Display all the fonts.
This sketch uses the GLCD (font 1) and fonts 2, 4, 6, 7, 8

#########################################################################
###### DON'T FORGET TO UPDATE THE User_Setup.h FILE IN THE LIBRARY ######
######       TO SELECT THE FONTS AND PINS YOU USE, SEE ABOVE       ######
#########################################################################
*/
// New background colour
#define TFT_BROWN 0x38E0
// Pause in milliseconds between screens, change to 0 to time font rendering
#define WAIT 1000
#include <TFT_eSPI.h> // Graphics and font library for ILI9341 driver chip
#include <SPI.h>
TFT_eSPI tft = TFT_eSPI();  // Invoke library, pins defined in User_Setup.h
unsigned long targetTime = 0; // Used for testing draw times
void setup(void) {
    tft.init();
    tft.setRotation(1);
}
void loop() {
    targetTime = millis();
// First we test them with a background colour set
tft.setTextSize(1);
tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 16, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 32, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 48, 2);
int xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 64, 2);
tft.drawChar(127, xpos, 64, 2);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 26, 4);
tft.drawString("<=>?@ABCDE", 0, 52, 4);
tft.drawString("FGHIJKLMNO", 0, 78, 4);
tft.drawString("PQRSTUVWX", 0, 104, 4);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 26, 4);
tft.drawString("pqrstuvwxyz", 0, 52, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 78, 4);
tft.drawChar(127, xpos, 78, 4);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_BLUE, TFT_BLACK);

tft.drawString("012345", 0, 0, 6);
tft.drawString("6789", 0, 40, 6);
tft.drawString("apm-:.", 0, 80, 6);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_RED, TFT_BLACK);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_YELLOW, TFT_BLACK);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 72, 8);
delay(WAIT);;

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 72, 8);
delay(WAIT);;

tft.setTextSize(2);
tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 32, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 64, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 96, 2);
xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 128, 2);
tft.drawChar(127, xpos, 128, 2);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_GREEN, TFT_BLACK);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 52, 4);
tft.drawString("<=>?@ABCDE", 0, 104, 4);
tft.drawString("FGHIJKLMNO", 0, 156, 4);
tft.drawString("PQRSTUVWX", 0, 208, 4);
delay(WAIT);
tft.fillScreen(TFT_BLACK);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 52, 4);
tft.drawString("pqrstuvwxyz", 0, 104, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 156, 4);
tft.drawChar(127, xpos, 156, 4);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_BLUE, TFT_BLACK);

tft.drawString("01234", 0, 0, 6);
tft.drawString("56789", 0, 80, 6);
tft.drawString("apm-:.", 0, 160, 6);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_RED, TFT_BLACK);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BLACK);
tft.setTextColor(TFT_YELLOW, TFT_BLACK);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 144, 8);
delay(WAIT);;

tft.fillScreen(TFT_BLACK);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 144, 8);
delay(WAIT);;

tft.setTextColor(TFT_MAGENTA, TFT_BROWN);

tft.drawNumber(millis() - targetTime, 0, 180, 4);
delay(4000);;

// Now test them with transparent background
targetTime = millis();

tft.setTextSize(1);
tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 16, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 32, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 48, 2);
xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 64, 2);
tft.drawChar(127, xpos, 64, 2);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 26, 4);
tft.drawString("<=>?@ABCDE", 0, 52, 4);
tft.drawString("FGHIJKLMNO", 0, 78, 4);
tft.drawString("PQRSTUVWX", 0, 104, 4);

delay(WAIT);
tft.fillScreen(TFT_BROWN);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 26, 4);
tft.drawString("pqrstuvwxyz", 0, 52, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 78, 4);
tft.drawChar(127, xpos, 78, 4);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_BLUE);

tft.drawString("012345", 0, 0, 6);
tft.drawString("6789", 0, 40, 6);
tft.drawString("apm-:.", 0, 80, 6);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_RED);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 60, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_YELLOW);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 72, 8);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 72, 8);
delay(WAIT);

tft.setTextSize(2);
tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-./0123456", 0, 0, 2);
tft.drawString("789:;<=>?@ABCDEFGHIJKL", 0, 32, 2);
tft.drawString("MNOPQRSTUVWXYZ[\\]^_`", 0, 64, 2);
tft.drawString("abcdefghijklmnopqrstuvw", 0, 96, 2);
xpos = 0;
xpos += tft.drawString("xyz{|}~", 0, 128, 2);
tft.drawChar(127, xpos, 128, 2);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_GREEN);

tft.drawString(" !\"#$%&'()*+,-.", 0, 0, 4);
tft.drawString("/0123456789:;", 0, 52, 4);
tft.drawString("<=>?@ABCDE", 0, 104, 4);
tft.drawString("FGHIJKLMNO", 0, 156, 4);
tft.drawString("PQRSTUVWX", 0, 208, 4);
delay(WAIT);
tft.fillScreen(TFT_BROWN);
tft.drawString("YZ[\\]^_`abc", 0, 0, 4);
tft.drawString("defghijklmno", 0, 52, 4);
tft.drawString("pqrstuvwxyz", 0, 104, 4);
xpos = 0;
xpos += tft.drawString("{|}~", 0, 156, 4);
tft.drawChar(127, xpos, 156, 4);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_BLUE);

tft.drawString("01234", 0, 0, 6);
tft.drawString("56789", 0, 80, 6);
tft.drawString("apm-:.", 0, 160, 6);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_RED);

tft.drawString("0123", 0, 0, 7);
tft.drawString("4567", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 7);
tft.drawString("", 0, 120, 7);
delay(WAIT);

tft.fillScreen(TFT_BROWN);
tft.setTextColor(TFT_YELLOW);

tft.drawString("0123", 0, 0, 8);
tft.drawString("4567", 0, 144, 8);
delay(WAIT);;

tft.fillScreen(TFT_BROWN);
tft.drawString("890:.", 0, 0, 8);
tft.drawString("", 0, 144, 8);
delay(WAIT);;

tft.setTextColor(TFT_MAGENTA);

tft.drawNumber(millis() - targetTime, 0, 180, 4);
delay(4000);;
}
```



### Configurable Buttons Example Code
```cpp
void setup() {
    Serial.begin(115200);
    pinMode(WIO_KEY_A, INPUT_PULLUP);
    pinMode(WIO_KEY_B, INPUT_PULLUP);
    pinMode(WIO_KEY_C, INPUT_PULLUP);
}
void loop() {
    // put your main code here, to run repeatedly:
    if (digitalRead(WIO_KEY_A) == LOW) {
        Serial.println("A Key pressed");
    }
    else if (digitalRead(WIO_KEY_B) == LOW) {
        Serial.println("B Key pressed");
    }
    else if (digitalRead(WIO_KEY_C) == LOW) {
        Serial.println("C Key pressed");
    }
    delay(200);
}
```

